#coding=utf-8
"""
测试数据分析功能的脚本
"""
import pandas as pd
import os
from datetime import datetime

def create_test_data():
    """创建测试数据"""
    
    # 创建测试目录
    os.makedirs('f:/work', exist_ok=True)
    
    # 创建测试的每日汇总数据
    daily_summary_data = [
        {
            '日期': 20241201,
            '总资产': 1000000.00,
            '可用资金': 200000.00,
            '冻结资金': 0.00,
            '总持仓市值': 800000.00,
            '普通股票市值': 500000.00,
            'ETF市值': 200000.00,
            '可转债市值': 100000.00,
            '基金市值': 0.00,
            '当日买入金额': 50000.00,
            '当日卖出金额': 30000.00,
            '当日净买入': 20000.00,
            '已实现盈亏': 1500.00,
            '交易笔数': 8,
            '更新时间': '15:15:00'
        },
        {
            '日期': 20241202,
            '总资产': 1005000.00,
            '可用资金': 180000.00,
            '冻结资金': 0.00,
            '总持仓市值': 825000.00,
            '普通股票市值': 520000.00,
            'ETF市值': 205000.00,
            '可转债市值': 100000.00,
            '基金市值': 0.00,
            '当日买入金额': 40000.00,
            '当日卖出金额': 60000.00,
            '当日净买入': -20000.00,
            '已实现盈亏': 2800.00,
            '交易笔数': 6,
            '更新时间': '15:15:00'
        },
        {
            '日期': 20241203,
            '总资产': 1012000.00,
            '可用资金': 190000.00,
            '冻结资金': 0.00,
            '总持仓市值': 822000.00,
            '普通股票市值': 515000.00,
            'ETF市值': 207000.00,
            '可转债市值': 100000.00,
            '基金市值': 0.00,
            '当日买入金额': 35000.00,
            '当日卖出金额': 25000.00,
            '当日净买入': 10000.00,
            '已实现盈亏': 1200.00,
            '交易笔数': 4,
            '更新时间': '15:15:00'
        }
    ]
    
    # 创建测试的交易明细数据
    transactions_data = [
        {
            '交易日期': 20241201,
            '交易时间': '09:35:00',
            '证券代码': '000001.SZ',
            '证券名称': '平安银行',
            '证券类型': '深市股票',
            '方向': '买入',
            '成交数量': 1000,
            '成交价格': 12.50,
            '成交金额': 12500.00,
            '已实现盈亏': 0.00
        },
        {
            '交易日期': 20241201,
            '交易时间': '10:15:00',
            '证券代码': '510300.SH',
            '证券名称': '沪深300ETF',
            '证券类型': 'ETF',
            '方向': '买入',
            '成交数量': 5000,
            '成交价格': 4.20,
            '成交金额': 21000.00,
            '已实现盈亏': 0.00
        },
        {
            '交易日期': 20241201,
            '交易时间': '14:30:00',
            '证券代码': '113050.SZ',
            '证券名称': '南银转债',
            '证券类型': '可转债',
            '方向': '买入',
            '成交数量': 100,
            '成交价格': 105.50,
            '成交金额': 10550.00,
            '已实现盈亏': 0.00
        },
        {
            '交易日期': 20241202,
            '交易时间': '09:45:00',
            '证券代码': '000001.SZ',
            '证券名称': '平安银行',
            '证券类型': '深市股票',
            '方向': '卖出',
            '成交数量': 500,
            '成交价格': 12.80,
            '成交金额': 6400.00,
            '已实现盈亏': 150.00
        },
        {
            '交易日期': 20241202,
            '交易时间': '11:00:00',
            '证券代码': '510300.SH',
            '证券名称': '沪深300ETF',
            '证券类型': 'ETF',
            '方向': '卖出',
            '成交数量': 2000,
            '成交价格': 4.35,
            '成交金额': 8700.00,
            '已实现盈亏': 300.00
        }
    ]
    
    # 创建测试的资产数据
    assets_data = [
        {
            '日期': 20241201,
            '时间': '15:15:00',
            '总资产': 1000000.00,
            '可用资金': 200000.00,
            '冻结资金': 0.00,
            '持仓市值': 800000.00
        },
        {
            '日期': 20241202,
            '时间': '15:15:00',
            '总资产': 1005000.00,
            '可用资金': 180000.00,
            '冻结资金': 0.00,
            '持仓市值': 825000.00
        },
        {
            '日期': 20241203,
            '时间': '15:15:00',
            '总资产': 1012000.00,
            '可用资金': 190000.00,
            '冻结资金': 0.00,
            '持仓市值': 822000.00
        }
    ]
    
    # 创建测试的持仓数据
    positions_data = [
        {
            '日期': 20241201,
            '时间': '15:15:00',
            '证券代码': '000001.SZ',
            '证券名称': '平安银行',
            '持仓数量': 1000,
            '可用数量': 1000,
            '冻结数量': 0,
            '成本价': 12.50,
            '市值': 12500.00,
            '浮动盈亏': 0.00
        },
        {
            '日期': 20241201,
            '时间': '15:15:00',
            '证券代码': '510300.SH',
            '证券名称': '沪深300ETF',
            '持仓数量': 5000,
            '可用数量': 5000,
            '冻结数量': 0,
            '成本价': 4.20,
            '市值': 21000.00,
            '浮动盈亏': 0.00
        }
    ]
    
    # 保存到Excel文件
    excel_path = 'f:/work/trade_records.xlsx'
    
    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='w') as writer:
        pd.DataFrame(daily_summary_data).to_excel(writer, sheet_name='daily_summary', index=False)
        pd.DataFrame(transactions_data).to_excel(writer, sheet_name='transactions', index=False)
        pd.DataFrame(assets_data).to_excel(writer, sheet_name='assets', index=False)
        pd.DataFrame(positions_data).to_excel(writer, sheet_name='positions', index=False)
    
    print(f"测试数据已创建: {excel_path}")
    print("包含以下表格:")
    print("- daily_summary: 每日汇总数据")
    print("- transactions: 交易明细数据")
    print("- assets: 资产数据")
    print("- positions: 持仓数据")

def test_data_analysis():
    """测试数据分析功能"""
    try:
        excel_path = 'f:/work/trade_records.xlsx'
        
        if not os.path.exists(excel_path):
            print("测试数据文件不存在，正在创建...")
            create_test_data()
        
        # 读取并显示数据
        print("\n=== 每日汇总数据 ===")
        daily_summary = pd.read_excel(excel_path, sheet_name='daily_summary')
        print(daily_summary)
        
        print("\n=== 交易明细数据 ===")
        transactions = pd.read_excel(excel_path, sheet_name='transactions')
        print(transactions)
        
        print("\n=== 数据统计 ===")
        print(f"总交易天数: {len(daily_summary)}")
        print(f"总交易笔数: {len(transactions)}")
        print(f"总买入金额: {transactions[transactions['方向']=='买入']['成交金额'].sum():,.2f}")
        print(f"总卖出金额: {transactions[transactions['方向']=='卖出']['成交金额'].sum():,.2f}")
        print(f"总已实现盈亏: {transactions['已实现盈亏'].sum():,.2f}")
        
        # 按证券类型统计
        print("\n=== 按证券类型统计 ===")
        type_stats = transactions.groupby('证券类型')['成交金额'].sum()
        for sec_type, amount in type_stats.items():
            print(f"{sec_type}: {amount:,.2f}")
        
        print("\n测试数据分析完成！")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    test_data_analysis()
