# 股票交易系统数据分析功能说明

## 新增功能概述

本次更新为股票交易系统增加了强大的数据分析功能，包括：

1. **每日汇总统计**
2. **证券分类管理**
3. **数据分析界面**
4. **图表可视化**
5. **今日概况快速查看**

## 功能详细说明

### 1. 每日汇总统计

#### 新增Excel表格：daily_summary
- **总资产**：当日总资产金额
- **可用资金**：可用于交易的资金
- **冻结资金**：委托中冻结的资金
- **总持仓市值**：所有持仓的总市值
- **普通股票市值**：沪深股票市值
- **ETF市值**：ETF基金市值
- **可转债市值**：可转债市值
- **基金市值**：其他基金市值
- **当日买入金额**：当日买入总金额
- **当日卖出金额**：当日卖出总金额
- **当日净买入**：净买入金额（买入-卖出）
- **已实现盈亏**：当日已实现盈亏
- **交易笔数**：当日交易笔数

### 2. 证券分类管理

#### 自动识别证券类型
- **沪市股票**：6开头
- **深市股票**：0、3开头
- **ETF**：51、15开头
- **可转债**：11、12开头
- **基金**：50、16、17、18开头

#### 交易记录增强
- 在transactions表中新增"证券类型"列
- 便于按类型筛选和统计交易数据

### 3. 数据分析界面

#### 访问方式
- 点击持仓信息区域的"数据分析"按钮
- 弹出专门的数据分析对话框

#### 查询条件
- **日期选择**：可选择特定日期或全部日期
- **证券类型**：可按证券类型筛选
- **分析类型**：支持4种分析模式

#### 分析类型
1. **每日汇总**：显示每日资产汇总数据表格
2. **交易明细**：显示详细交易记录，支持按类型筛选
3. **持仓分析**：持仓详细分析（开发中）
4. **图表分析**：多种图表可视化

### 4. 图表可视化功能

#### 资产趋势图
- 显示总资产、可用资金、持仓市值的时间趋势
- 多条线图，便于对比分析
- 自动格式化数值和日期

#### 持仓分布图
- **饼图**：显示各类证券持仓比例
- **柱图**：显示各类证券持仓市值
- 实时数据，反映当前持仓结构

#### 交易统计图
- **每日交易金额**：按日期统计交易量
- **证券类型交易**：各类证券交易金额对比
- **买卖对比**：买入卖出金额对比
- **盈亏统计**：盈利亏损金额统计

### 5. 今日概况功能

#### 访问方式
- 点击持仓信息区域的"今日概况"按钮
- 弹出今日资产和交易概况

#### 显示内容
- **资产概况**：总资产、可用资金、持仓市值
- **持仓分布**：各类证券市值和占比
- **交易情况**：买入、卖出、净买入、已实现盈亏
- **交易笔数**：当日交易次数

## 使用指南

### 首次使用
1. 运行程序后，系统会自动创建每日汇总数据
2. 在15:15自动保存当日数据到Excel
3. 可手动点击"交易记录"保存数据

### 查看数据分析
1. 点击"数据分析"按钮
2. 选择查询条件（日期、证券类型、分析类型）
3. 点击"查询"按钮查看结果
4. 在图表分析中可切换不同图表类型

### 快速查看今日情况
1. 点击"今日概况"按钮
2. 查看弹出的概况信息
3. 包含资产分布和交易统计

## Excel文件结构

### 文件位置
`f:/work/trade_records.xlsx`

### 表格说明
- **daily_summary**：每日汇总数据（新增）
- **transactions**：交易明细（增加证券类型列）
- **assets**：资产变化记录
- **positions**：持仓记录

## 注意事项

1. **数据完整性**：确保每日15:15后数据已保存
2. **图表显示**：需要matplotlib库支持
3. **文件路径**：确保f:/work目录存在且可写
4. **数据累积**：数据会累积保存，形成历史记录

## 技术特点

- **实时计算**：持仓分布实时计算当前数据
- **历史分析**：支持历史数据的多维度分析
- **可视化**：丰富的图表展示，直观易懂
- **分类统计**：自动识别证券类型，精确分类
- **数据导出**：Excel格式，便于进一步分析

## 后续扩展

- 增加更多图表类型
- 添加收益率分析
- 支持自定义时间段分析
- 增加风险指标计算
- 支持数据导出为其他格式

通过这些新功能，您可以更直观地了解每日的资产变化、交易情况和持仓分布，为投资决策提供有力支持。
